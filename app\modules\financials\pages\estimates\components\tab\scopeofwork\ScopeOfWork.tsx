// Atoms
import { Spin } from "~/shared/components/atoms/spin";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
// store
import { useTranslation } from "~/hook";
import { useCallback, useEffect, useRef, useState } from "react";
import ScopeItemList from "./ScopeItemList";
import { useAppESDispatch, useAppESSelector } from "../../../redux/store";
import {
  getEstimateUpdateScopeDetail,
  updateEstimateScopeDetailApi,
} from "../../../redux/action/ESDetailAction";
import {
  failUpdateScoreWorkDetail,
  onChangeScopeWorkDetail,
  successUpdateScoreWorkDetail,
  updateScopeWorkDetail,
} from "../../../redux/slices/ESScopeWorkSlice";
import delay from "lodash/delay";
import Sortable from "sortablejs";

const ScopeOfWork = ({ isReadOnly }: IEReadOnlyComponent) => {
  const { _t } = useTranslation();
  const dispatch = useAppESDispatch();
  const taskListRef = useRef<HTMLDivElement | null>(null);
  const {
    isScopeWorkLoading,
    previous_Task,
    scopeWorkDetail,
  }: IEstimatesDetailState = useAppESSelector(
    (state) => state.getEstimateScopeDetail
  );
  const { estimateDetail = {} } = useAppESSelector(
    (state) => state.estimateDetail
  );
  // developer
  const inputRefs = useRef<any>({});
  // Step 1: Define loading state for check box
  const [isLoadingCheckBox, setIsLoadingCheckBox] = useState<
    Record<number, boolean>
  >({});
  // Step 3: Define loading state for task drag and drop in one section
  const [
    isLoadingTaskDragAndDropInSection,
    setIsLoadingTaskDragAndDropInSection,
  ] = useState<Record<number, boolean>>({});
  // Step 4: Define loading state for task name update
  // const [isLoadingTaskName, setIsLoadingTaskName] = useState<
  //   Record<string, IStatus>
  // >({});

  // Add a new ref for every task
  const addRef = (taskIndex: number, ref: any) => {
    inputRefs.current[taskIndex] = ref;
  };

  const handleTaskStatus = async (
    status: boolean,
    // sectionId: number,
    taskId: number
  ) => {
    try {
      if (!!estimateDetail?.estimate_id && !!taskId) {
        setIsLoadingCheckBox((prev) => ({ ...prev, [taskId]: true }));
        const updateStatus = (scopeWorkDetail?.checklist ?? [])?.map(
          (task: ESChecklistItem) => {
            return {
              ...task,
              status: task.item_id === taskId ? (status ? 1 : 0) : task.status,
              reference_item_id: task.reference_item_id ?? 0,
            };
          }
        );

        const payload: ESScopeWorkDetailData = {
          checklist: updateStatus?.filter(
            (data: ESChecklistItem) => data.item_id != -1
          ),
          // .filter((data: ESChecklistItem) => data?.task_name?.trim() != ""),
        };
        const isUpdateCheckBoxStatus = (await updateEstimateScopeDetailApi({
          estimate_id: estimateDetail?.estimate_id,
          ...payload,
        })) as IEstimateScopeWorkDetailApiRes;

        if (isUpdateCheckBoxStatus?.success) {
          dispatch(updateScopeWorkDetail({ checklist: updateStatus }));
        } else {
          // dispatch(failUpdateSectionToDoDetailPage(previous_sections ?? []));
          notification.error({
            description:
              isUpdateCheckBoxStatus?.message || "Something went wrong!",
          });
        }
      } else {
        // dispatch(failUpdateSectionToDoDetailPage(previous_sections ?? []));
        notification.error({
          description: "Something went wrong!",
        });
      }
    } catch (err) {
      notification.error({
        description: (err as Error).message || "Something went wrong!",
      });
    } finally {
      setIsLoadingCheckBox((prev) => ({ ...prev, [taskId]: false }));
    }
  };
  const moveTask = async ({
    fromTaskId,
    toTaskId,
    fromIndex,
    toIndex,
    taskId,
  }: IEMoveWorkTaskParmas) => {
    try {
      // if (fromTaskId === toTaskId) {
      setIsLoadingTaskDragAndDropInSection((prev) => ({
        ...prev,
        [taskId]: true,
      }));
      const findTask =
        (scopeWorkDetail?.checklist ?? [])?.filter(
          (task: ESChecklistItem) => task.item_id != -1
        ) ?? [];

      const taskRemove = findTask.find(
        (data: ESChecklistItem) => data.item_id == taskId
      );

      const taskFilter = findTask.filter(
        (data: ESChecklistItem) => data.item_id != taskId
      );

      if (taskRemove) taskFilter.splice(toIndex ?? 0, 0, taskRemove);

      const task_sorting: ESChecklistItem[] = taskFilter.map(
        (data: ESChecklistItem, index: number) => ({
          ...data,
          item_sort_order: index + 1,
        })
      );

      // Move task within the same section
      // Reorder tasks in the section identified by fromSectionId
      const payload: ESScopeWorkDetailData = {
        checklist: task_sorting?.filter(
          (data: ESChecklistItem) => data.item_id != -1
        ),
        // .filter((data: ESChecklistItem) => data?.task_name?.trim() != ""),
      };
      const isUpdateCLSection = (await updateEstimateScopeDetailApi({
        estimate_id: Number(estimateDetail?.estimate_id),
        ...payload,
      })) as IEstimateScopeWorkDetailApiRes;

      if (isUpdateCLSection?.success) {
        // setActiveStep(getTaskStatus(isUpdateCLSection?.data?.checklist));
        // dispatch(updateScopeWorkDetail({ checklist: task_sorting }));
        dispatch(successUpdateScoreWorkDetail(payload?.checklist ?? []));
      } else {
        // dispatch(failUpdateSectionToDoDetailPage(previous_sections ?? []));
        notification.error({
          description: isUpdateCLSection?.message || "Something went wrong!",
        });
      }
      // }
    } catch (err) {
      // dispatch(failUpdateSectionToDoDetailPage(previous_sections ?? []));
      notification.error({
        description: (err as Error).message || "Something went wrong!",
      });
    } finally {
      setIsLoadingTaskDragAndDropInSection((prev) => ({
        ...prev,
        [taskId]: false,
      }));
      delay(() => {
        //if debouce set
      }, 3000);
    }
  };
  const initializeSortable = useCallback(() => {
    if (taskListRef.current) {
      const sortable = Sortable.create(taskListRef.current, {
        group: "shared",
        animation: 150,
        handle: ".drag-handle",
        filter: ".undraggable",
        onEnd: (evt: any) => handleTaskDrop(evt),
      });
      // Clean up on component unmount
      return () => sortable.destroy();
    }
  }, [JSON.stringify(scopeWorkDetail?.checklist), moveTask]);

  useEffect(() => {
    const cleanupSortable = initializeSortable();
    return cleanupSortable; // Cleanup sortable instance on unmount or section change
  }, [initializeSortable]);

  const handleTaskDrop = (evt: Sortable.SortableEvent) => {
    const { oldIndex, newIndex, from, to } = evt;
    const nextTask = scopeWorkDetail.checklist[newIndex ?? 0];
    // Prevent dropping below a non-draggable task
    if (nextTask?.item_id === -1) {
      evt.from.insertBefore(evt.item, evt.from.children[oldIndex ?? 0]);
      return;
    }

    const fromSectionId = from.getAttribute("data-section-id");
    const toSectionId = to.getAttribute("data-section-id");

    if (
      fromSectionId === "-1" ||
      toSectionId === "-1" ||
      scopeWorkDetail.checklist[oldIndex ?? 0]?.item_id === -1
    ) {
      return;
    }

    const movedTaskId = scopeWorkDetail.checklist[oldIndex ?? 0]?.item_id;
    if (fromSectionId && toSectionId && movedTaskId) {
      moveTask({
        fromTaskId: fromSectionId,
        toTaskId: toSectionId,
        fromIndex: oldIndex,
        toIndex: newIndex,
        taskId: movedTaskId,
      });
    }
  };

  // const handleUpdateTaskName = (
  //   event: React.ChangeEvent<HTMLTextAreaElement>,
  //   taskId: number,
  //   taskIndex: number
  // ) => {
  //   setIsLoadingTaskName((prev) => ({
  //     ...prev,
  //     [`${taskIndex}`]: "save",
  //   }));
  //   if (taskId != -1) {
  //     const updateTaskName = (scopeWorkDetail?.checklist ?? [])?.map(
  //       (task: ESChecklistItem) => {
  //         return {
  //           ...task,
  //           task_name:
  //             task.item_id === taskId ? event.target.value : task?.task_name,
  //         };
  //       }
  //     );
  //     dispatch(
  //       onChangeScopeWorkDetail({
  //         checklist: updateTaskName,
  //       })
  //     );
  //   } else {
  //     // Map over the tasks and update the task field where item_id is -1
  //     const updatedTasks = scopeWorkDetail?.checklist?.map(
  //       (task: ESChecklistItem, tIndex: number) =>
  //         task.item_id == taskId && tIndex + 1 == taskIndex
  //           ? { ...task, task_name: event.target.value }
  //           : task
  //     );

  //     // Create a new section with the updated tasks
  //     // const updatedSection = { ...section, tasks: updatedTasks };
  //     dispatch(
  //       onChangeScopeWorkDetail({
  //         checklist: updatedTasks,
  //       })
  //     );
  //   }
  // };

  // const handleBlurUpdateTaskName = async (
  //   event: React.ChangeEvent<HTMLTextAreaElement>,
  //   // sectionId: number,
  //   taskId: number,
  //   // sectionIndex: number,
  //   taskIndex: number
  // ) => {
  //   const updateName = (event.target as HTMLTextAreaElement).value;
  //   try {
  //     const getTaskName =
  //       (previous_Task?.checklist || [])
  //         ?.find(
  //           (task: ESChecklistItem, tIndex: number) =>
  //             (task.item_id == taskId || task.item_id == -1) &&
  //             tIndex + 1 == taskIndex
  //         )
  //         ?.task_name?.trim() ?? "";
  //     if (getTaskName != updateName) {
  //       setIsLoadingTaskName((prev) => ({
  //         ...prev,
  //         [`${taskIndex}`]: "loading",
  //       }));

  //       const getTask =
  //         scopeWorkDetail?.checklist
  //           ?.find((data: ESChecklistItem) => data?.task_name?.trim() !== "")
  //           ?.task_name?.trim() ?? "";
  //       // if (!!getTask) {
  //       const updateTaskName = (scopeWorkDetail?.checklist ?? [])
  //         ?.map((task: ESChecklistItem, index: number) => {
  //           if (taskId == -1 && task.item_id === taskId) {
  //             if (task?.task_name?.trim() === "") {
  //               return null;
  //             }
  //             return {
  //               item_id: 0,
  //               reference_item_id: 0,
  //               status: task?.status ?? 0,
  //               task_name:
  //                 task.item_id === taskId
  //                   ? event.target.value
  //                   : task?.task_name,
  //             };
  //           } else {
  //             return {
  //               item_id: task?.item_id ?? 0,
  //               status: task?.status ?? 0,
  //               reference_item_id: task?.reference_item_id ?? 0,
  //               task_name:
  //                 task.item_id === taskId
  //                   ? event.target.value
  //                   : task?.task_name,
  //             };
  //           }
  //         })
  //         ?.filter((data: ESChecklistItem) => data);

  //       let payload: ESScopeWorkDetailData = {
  //         checklist: updateTaskName
  //           ?.map((data: ESChecklistItem, index: number) => {
  //             if (data?.item_id != -1 && data?.task_name?.trim() != "")
  //               return {
  //                 item_id: data?.item_id ?? 0,
  //                 task_name: data?.task_name ?? "",
  //                 reference_item_id: data?.reference_item_id ?? 0,
  //                 status: data?.status ?? 0,
  //                 // item_sort_order: index,
  //               };
  //           })
  //           ?.filter((data: ESChecklistItem) => data),
  //       };
  //       // const isItemZero: ESChecklistItem | undefined =
  //       //   payload?.checklist?.find((el) => el?.item_id == 0);

  //       // if (isItemZero) {
  //       //   payload = {
  //       //     checklist: [isItemZero],
  //       //   };
  //       // }

  //       const isUpdateCLSection = (await updateEstimateScopeDetailApi({
  //         estimate_id: estimateDetail?.estimate_id,
  //         ...payload,
  //       })) as IEstimateScopeWorkDetailApiRes;
  //       if (isUpdateCLSection?.success) {
  //         // if (taskId == -1) {
  //         dispatch(successUpdateScoreWorkDetail(payload?.checklist));
  //         const isItemZero = payload?.checklist?.find((el) => el?.item_id == 0);

  //         if (isItemZero) {
  //           dispatch(
  //             getEstimateUpdateScopeDetail({
  //               estimate_id: estimateDetail?.estimate_id,
  //             })
  //           );
  //         }
  //         // if()
  //         // } else {
  //         //   dispatch(updateScopeWorkDetail({ checklist: payload?.checklist }));
  //         // }
  //         setIsLoadingTaskName((prev) => ({
  //           ...prev,
  //           [`${taskIndex}`]: "success",
  //         }));
  //       } else {
  //         notification.error({
  //           description: isUpdateCLSection?.message || "Something went wrong!",
  //         });
  //         failUpdateScoreWorkDetail(previous_Task);
  //         setIsLoadingTaskName((prev) => ({
  //           ...prev,
  //           [`${taskIndex}`]: "error",
  //         }));
  //       }
  //       // } else {
  //       //   dispatch(
  //       //     failUpdateScoreWorkDetail({
  //       //       checklist: previous_Task?.checklist,
  //       //     })
  //       //   );
  //       //   notification.error({
  //       //     description: "Task Name is required.",
  //       //   });
  //       //   setIsLoadingTaskName((prev) => ({
  //       //     ...prev,
  //       //     [`${taskIndex}`]: "error",
  //       //   }));
  //       // }
  //     } else {
  //       // dispatch(failUpdateScoreWorkDetail({ ...previous_Task }));
  //       // setIsLoadingTaskName((prev) => ({
  //       //   ...prev,
  //       //   [`${taskIndex}`]: "success",
  //       // }));
  //     }
  //     delay(() => {
  //       setIsLoadingTaskName((prev) => ({
  //         ...prev,
  //         [`${taskIndex}`]: "button",
  //       }));
  //     }, 500);
  //   } catch (err) {
  //     notification.error({
  //       description: (err as Error).message || "Something went wrong!",
  //     });
  //     setIsLoadingTaskName((prev) => ({
  //       ...prev,
  //       [`${taskIndex}`]: "error",
  //     }));
  //   } finally {
  //     setTimeout(() => {
  //       const nextIndex = taskIndex + 1;
  //       const nextRef = inputRefs.current[nextIndex];
  //       if (nextRef) {
  //         nextRef.focus();
  //       }
  //     }, 0);
  //   }
  // };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Scope of Work (Checked items show on PDF)")}
        iconProps={{
          icon: "fa-solid fa-memo-pad",
          containerClassName:
            "bg-[linear-gradient(180deg,#4776E61a_0%,#8E54E91a_100%)]",
          id: "scope_work_icon",
          colors: ["#4776E6", "#8E54E9"],
        }}
        children={
          <>
            {scopeWorkDetail?.checklist?.length > 0 ? (
              !isReadOnly || scopeWorkDetail?.checklist[0]?.item_id != -1 ? (
                <div className="bg-[#F8F8F8] p-2.5 mt-2 rounded group/checklist dark:!bg-dark-900 dark:text-white/90">
                  {/* List of tasks */}
                  {isScopeWorkLoading ? (
                    <Spin className="w-full h-[260px] flex items-center justify-center" />
                  ) : (
                    <div
                      ref={taskListRef}
                      key={"work score"}
                      data-section-id={"sectionId"}
                    >
                      {(scopeWorkDetail?.checklist ?? []).map(
                        (task: ESChecklistItem, taskIndex: number) => (
                          <ScopeItemList
                            key={`${task.item_id}-${taskIndex + 1}`}
                            task={task}
                            taskIndex={taskIndex + 1}
                            // sectionId={section.section_id}
                            handleTaskStatus={handleTaskStatus}
                            // onInputUpdateTaskName={handleUpdateTaskName}
                            // onBlurUpdateTaskName={handleBlurUpdateTaskName}
                            inputRefs={inputRefs}
                            isReadOnly={isReadOnly}
                            // index={index}
                            loadingProps={{
                              isLoadingCheckBox,
                              setIsLoadingCheckBox,
                            }}
                            // loadingSectionUpdate={loadingSectionUpdate}
                            loadingMoveTaskInSection={{
                              isLoadingTaskDragAndDropInSection,
                              setIsLoadingTaskDragAndDropInSection,
                            }}
                            // loadingTaskName={{
                            //   isLoadingTaskName,
                            //   setIsLoadingTaskName,
                            // }}
                            addRef={addRef}
                          />
                        )
                      )}
                    </div>
                  )}
                </div>
              ) : (
                <NoRecords
                  image={`${window.ENV.CDN_URL}assets/images/no-records-scope-work.svg`}
                />
              )
            ) : (
              <NoRecords
                image={`${window.ENV.CDN_URL}assets/images/no-records-scope-work.svg`}
              />
            )}
            {/* </div> */}

            {/* <div
              className={`bg-white px-2.5 py-0.5  rounded flex items-center justify-between dark:bg-dark-400 dark:text-white/90`}
            > */}
            {/* {!isReadOnly && ( */}
            {/* <div className="relative">
              <ButtonWithTooltip
                icon="fa-solid fa-grip-dots"
                tooltipTitle={_t("Move")}
                tooltipPlacement="top"
                iconClassName="w-3.5 h-3.5 drag-handle undraggable cursor-not-allowed opacity-50"
                className="cursor-move undraggable  opacity-50"
                onClick={() => {}}
              />
              <div className="w-[18px] h-[18px] bg-white absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex items-center rounded-[5px]">
                <FontAwesomeIcon
                  className="h-[18px] w-[18px] text-primary-900 fa-spin"
                  icon={faSpinnerThird}
                />
              </div>
            </div> */}
            {/* // )} */}

            {/* <div className="flex gap-2 items-center w-[calc(100%-32px)]">
                <CustomCheckBox name="status" />
                <TextAreaField
                  placeholder={_t("Add Scope of Work")}
                  labelPlacement="left"
                  className="h-[30px]"
                  readOnlyClassName="sm:min-h-[30px] py-1.5"
                  editInline={true}
                  iconView={true}
                />
              </div>
            </div> */}
          </>
        }
      />
    </>
  );
};

export default ScopeOfWork;
