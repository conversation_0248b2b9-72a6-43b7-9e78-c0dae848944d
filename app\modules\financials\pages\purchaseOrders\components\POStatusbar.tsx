// molecules
import { ProgressBarHeader } from "~/shared/components/molecules/ProgressBarHeader";
// Other
import { useTranslation } from "~/hook";
import { useAppPOSelector } from "../redux/store";
import { useState } from "react";
import useFieldStatus from "../utils/useFieldStatus ";
import { POfieldStatus } from "../utils/common";
import dayjs from "dayjs";
import { getGSettings } from "~/zustand";

const POStatusbar: React.FC<IPOStatusbarProps> = ({
  handleUpdateField = (
    data: IPODetailData = {},
    extraData?: IPODetailData
  ) => {},
  purchaseStatList,
  isReadOnly,
  activeStep,
  setActiveStep,
  isStatusLost,
  selectedStatusInd,
}) => {
  const { purchaseOrderDetail, isPODetailLoading } = useAppPOSelector(
    (state) => state.purchaseOrderDetail
  );

  const { _t } = useTranslation();

  const [clicked, setClicked] = useState<boolean>(false);

  const { loadingStatus, handleChangeFieldStatus, setLoadingStatus } =
    useFieldStatus(POfieldStatus);
  const handleStatusChange = async (val: string) => {
    await onIconClickChange(val);
  };

  const onIconClickChange = async (val: string) => {
    const selectedOption = purchaseStatList.find(
      (option) => option.label === val.toString()
    );
    // 09-July-2025: PO > New > Do not make the Supplier a required field unless the user is trying to change the status to Approved > Submitted > Received > Closed.  This change is necessary for another change that will allow the user submit pricing request from multiple suppliers.
    // https://app.clickup.com/t/86czjvb53 > Remove Required Project and Supplier fields in PO's
    if (
      ["po_approved", "po_submitted", "po_received", "po_paid"]?.includes(
        selectedOption?.value || ""
      ) &&
      (purchaseOrderDetail?.supplier_details?.length <= 0 ||
        !purchaseOrderDetail?.supplier_id ||
        purchaseOrderDetail?.supplier_id == "" ||
        purchaseOrderDetail?.supplier_id == 0)
    ) {
      setClicked(false);
      return notification.error({
        description: _t("Missing Supplier."),
      });
    }
    const existingselectedOption = purchaseStatList.find(
      (option) => option.value === activeStep
    );
    try {
      if (selectedOption?.value !== existingselectedOption?.value) {
        setActiveStep(selectedOption?.value!);
        const extraPaylaod: { delivery_date?: string; po_order_date?: string } =
          {};
        if (
          selectedOption?.key == "po_received" &&
          !!!purchaseOrderDetail?.delivery_date
        ) {
          extraPaylaod.delivery_date = dayjs()?.format(
            CFConfig.day_js_date_format
          );
        }
        if (
          selectedOption?.key == "po_submitted" &&
          !!!purchaseOrderDetail?.po_order_date
        ) {
          extraPaylaod.po_order_date = dayjs()?.format(
            CFConfig.day_js_date_format
          );
        }

        // if (
        //   purchaseOrderDetail?.supplier_details?.length > 0 &&
        //   (!!purchaseOrderDetail?.supplier_id ||
        //     purchaseOrderDetail?.supplier_id != 0)
        // ) {
        await handleUpdateField(
          {
            billing_status: Number(selectedOption?.item_id),
            billing_status_key: selectedOption?.value,
            billing_status_name: selectedOption
              ? selectedOption.label
              : "Select Status",
            purchase_order_id:
              Number(purchaseOrderDetail?.purchase_order_id) || 0,
            is_multiple_suppliers: purchaseOrderDetail?.is_multiple_suppliers,
            supplier_id: purchaseOrderDetail?.supplier_id || 0,
            supplier_contact_id: purchaseOrderDetail?.supplier_contact_id || 0,
            po_suppliers: purchaseOrderDetail?.po_suppliers,
            // customer_id: purchaseOrderDetail?.customer_id,
          },
          extraPaylaod
        );
        // }

        setClicked(false);
      } else {
        handleChangeFieldStatus({
          field: "billing_status",
          status: "button",
          action: "BLUR",
        });
        setActiveStep(existingselectedOption?.value!);

        setClicked(false);
      }
    } catch (error) {
      setActiveStep(existingselectedOption?.value!);
    }
  };
  return (
    <>
      {purchaseStatList
        ?.filter((item) => item?.show_in_progress_bar)
        ?.map((item: EStatusList, index: number) => {
          // const isActive = Number(activeStep) >= Number(item.value);
          const isActive =
            selectedStatusInd != undefined && selectedStatusInd >= index;
          // const statusTooltipName = "status_" + (item.value || index);
          // const emailTooltipName = "email_" + (item.value || index);
          return (
            <>
              {item && (
                <li
                  key={index}
                  onClick={() => {}}
                  className={`${
                    isStatusLost ? "cursor-not-allowed" : ""
                  } relative 2xl:min-w-[125px] xl:min-w-24 lg:w-24 w-20 grid justify-end first:before:hidden before:absolute before:h-[2px] before:!w-[calc(100%-54px)]  ${
                    isActive ? "before:bg-primary-900" : "before:bg-[#ACAEAF]"
                  } before:top-[30%] lg:before:left-[-22px] before:left-[-13px]`}
                >
                  <ProgressBarHeader
                    option={item as EStatusList}
                    isActive={isActive}
                    isReadOnly={isReadOnly}
                    onClick={(e: IProgressBarHeaderPropOption) => {
                      if (isReadOnly || isStatusLost) {
                        return;
                      }
                      if (e.label && !clicked) {
                        setClicked(true);
                        return handleStatusChange(e.label);
                      }
                    }}
                  />
                </li>
              )}
            </>
          );
        })}
    </>
  );
};

export default POStatusbar;
