// Atoms
import { <PERSON><PERSON><PERSON> } from "~/shared/components/atoms/tooltip";
import { Popover } from "~/shared/components/atoms/popover";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { IconButton } from "~/shared/components/molecules/iconButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
// Hook
import { useTranslation } from "~/hook";
import { useParams } from "@remix-run/react";
import { getGConfig, getGSettings } from "~/zustand";
import { useAppPODispatch, useAppPOSelector } from "../../../redux/store";
import {
  backendDateFormat,
  displayDateFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import {
  filterOptionBySubstring,
  getStatusForField,
  roundToAmount,
} from "~/shared/utils/helper/common";
import useFieldStatus from "../../../utils/useFieldStatus ";
import { POfieldStatus } from "../../../utils/common";
import dayjs from "dayjs";
import {
  getPODetailNotReload,
  updatePODetailApi,
} from "../../../redux/action/PODetailAction";
import { updatePODetail } from "../../../redux/slices/poDetailSlice";
import delay from "lodash/delay";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import { useEffect, useMemo, useState } from "react";
import { getDirectaryKeyById } from "~/components/sidebars/multi-select/customer/zustand/action";
import {
  addItemObject,
  OrderTypeList,
  PODetailsFields,
} from "../../../utils/constants";
import useInvoiceTermsList from "~/shared/hooks/useGetTerms";
import { addInvoiceTerms } from "~/redux/action/invoiceTermsAction";
import { addInvoiceTermsAct } from "~/redux/slices/invoiceTermsSlice";
import {
  convertToFixed,
  HtmlDecode,
  numOrTextFind,
} from "../../../utils/function";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import { AvatarIconPopover } from "~/shared/components/organisms/avatarIconPopover";

const DetailsCard = ({
  isReadOnly,
  POMatrix,
  isTaxEnabled,
}: POReadOnlyComponentwithPOMatrix) => {
  const { _t } = useTranslation();
  const { id: purchase_order_id }: RouteParams = useParams();
  const { date_format }: GSettings = getGSettings();
  const dispatch = useAppPODispatch();
  const gConfig: GConfig = getGConfig();
  const { formatter } = useCurrencyFormatter();
  const { purchaseOrderDetail, isPODetailLoading } = useAppPOSelector(
    (state) => state?.purchaseOrderDetail
  );
  const termsList: IInvoiceTermsList[] = useInvoiceTermsList();

  const { loadingStatus, handleChangeFieldStatus, setLoadingStatus } =
    useFieldStatus(POfieldStatus);
  const [isOpenSelectOrderFrom, setIsOpenSelectOrderFrom] =
    useState<boolean>(false);
  const [inputValues, setInputValues] =
    useState<IPODetailData>(PODetailsFields);
  const [customDataAdd, setCustomDataAdd] = useState<ICommonCustomDataFrm>({});
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [isOpenSelectSupplierTo, setIsOpenSelectSupplierTo] =
    useState<boolean>(false);
  const [isMultipleSupplier, setIsMultipleSupplier] = useState<number>(-1);
  const [supplierTo, setSupplierTo] = useState<TselectedContactSendMail[]>([]);
  const [contactId, setcontactId] = useState<number>();
  const [additionalContact, setAdditionContact] = useState<number>(0);
  const [isContactDetails, setIsContactDetails] = useState<boolean>(false);
  const [isOpenProjectManagerDetails, setIsOpenProjectManagerDetails] =
    useState<boolean>(false);
  const supplierOptionKey: CustomerEmailTab[] = useMemo(() => {
    const keys: CustomerEmailTab[] = [
      CFConfig.vendor_key as CustomerEmailTab,
      CFConfig.contractor_key as CustomerEmailTab,
      CFConfig?.misc_contact_key as CustomerEmailTab,
      "by_service" as CustomerEmailTab,
    ];
    if (purchaseOrderDetail?.pro_id != 0) {
      keys.push("my_project" as CustomerEmailTab);
    }
    return keys;
  }, [purchaseOrderDetail]);
  useEffect(() => {
    if (purchaseOrderDetail) setInputValues(purchaseOrderDetail);
  }, [purchaseOrderDetail]);

  const handleInpOnChange = (
    e: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>,
    key: string
  ) => {
    const { name, value } = e.target;
    setInputValues({ ...inputValues, [key]: value });
  };
  const handleChange = ({
    value,
    name,
    extraValue,
  }: ISingleSelectOption & { extraValue?: IPODetailData }) => {
    const newValue = typeof value === "string" ? value : value[0];
    setInputValues({
      ...inputValues,
      [name]: newValue,
      ...(extraValue || {}),
    });
    handleUpdateField({
      [name]: newValue,
      ...(extraValue || {}),
      purchase_order_id: Number(purchase_order_id) || 0,
    });
  };

  const handlekeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (event.key === "Enter") {
      const value = event?.currentTarget?.value?.trim();
      const newType = onEnterSelectSearchValue(event, termsList || []);
      if (newType) {
        setCustomDataAdd({
          name: HTMLEntities.decode(newType),
        });
        setIsConfirmDialogOpen(true);
      } else if (value) {
        notification.error({
          description: "Records already exist, no new records were added.",
        });
      }
    }
  };

  const closeConfirmationModal = () => {
    setIsConfirmDialogOpen(false);
  };

  const handleAddCustomData = async () => {
    if (!isAddingCustomData && customDataAdd?.name) {
      setIsAddingCustomData(true);

      const temDataRes = (await addInvoiceTerms({
        name: customDataAdd?.name,
      })) as ICustomDataAddUpRes;

      const newTermId: string = temDataRes?.data?.item_id?.toString();
      const isKeyValue = numOrTextFind(temDataRes?.data?.item_id);
      if (temDataRes?.success) {
        dispatch(addInvoiceTermsAct(temDataRes?.data));
        setInputValues({
          ...inputValues,
          term_id: newTermId,
        });

        handleUpdateField({
          term_key: isKeyValue?.toString(),
          term_id:
            isKeyValue == "number" ? String(temDataRes?.data?.item_id) : "0",
          purchase_order_id: Number(purchase_order_id) || 0,
        });

        setIsConfirmDialogOpen(false);
      } else {
        notification.error({
          description: temDataRes?.message,
        });
      }
      setIsAddingCustomData(false);
    }
  };

  const handleUpdateField = async (data: IPODetailData) => {
    const field = Object.keys(data)[0];
    const values = Object.values(data)[0] as string;
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updatePODetailApi({
      ...data,
      purchase_order_id,
    })) as IPODetailApiRes;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });

      if (["po_order_date", "order_date", "delivery_date"]?.includes(field)) {
        dispatch(
          updatePODetail({
            // ...data,
            [field]: !!values ? dayjs(values).format(date_format) : "",
          })
        );
        // dispatch(getPODetail({ purchase_order_id }));
      } else {
        if (
          [
            "order_from",
            "po_suppliers",
            "is_multiple_suppliers",
            // "ship_to_contact",
          ]?.includes(field)
        ) {
          const response = await dispatch(
            getPODetailNotReload({ purchase_order_id })
          );
          const payload = response.payload as { data: IPODetailData };
          if (payload && payload.data?.purchase_order_id) {
            const datas: IPODetailData = payload.data;
            await dispatch(
              updatePODetail({ ...datas, ...(updateRes?.data ?? {}) })
            );
            if (field == "po_suppliers") {
              setIsMultipleSupplier(-1);
            }
            // if (field === "is_multiple_suppliers") {
            //   setIsOpenSelectSupplierTo(true);
            // }
          }
        } else {
          await dispatch(
            updatePODetail({ ...data, ...(updateRes?.data ?? {}) })
          );
        }
      }
      const detail = updateRes?.data?.detail;
      if (Object.keys(detail || {})?.length > 0) {
        dispatch(updatePODetail(detail));
      }
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });

      dispatch(
        updatePODetail({
          [field]: purchaseOrderDetail?.[field as keyof IPODetailData],
        })
      );
      // setInputValues({ ...purchaseOrderDetail, [field]: purchaseOrderDetail[field] });

      notification.error({
        description: updateRes?.message,
      });
    }
    resetFieldStatus(field);
  };

  const resetFieldStatus = (field: string) => {
    delay(() => {
      handleChangeFieldStatus({
        field: field,
        status: "button",
        action: "API",
      });
    }, 3000);
  };

  const handleChangeDate = (dateString: string, key: string) => {
    const formatDate = (date: string, type: string) => {
      return backendDateFormat(date, date_format);
    };
    setInputValues({
      ...inputValues,
      [key]: dateString,
    });
    const formattedDate =
      dateString && dateString !== "" ? formatDate(dateString, key) : null;
    if (purchaseOrderDetail?.[key as keyof IPODetailData] !== dateString) {
      handleUpdateField({
        [key]: formattedDate,
        purchase_order_id: Number(purchase_order_id) || 0,
        // customer_id: purchaseOrderDetail?.customer_id,
      });
    } else {
      dispatch(
        updatePODetail({
          [key]: purchaseOrderDetail?.[key as keyof IPODetailData],
        })
      );
    }
  };

  useEffect(() => {
    if (inputValues?.supplier_details?.length !== 0) {
      const assigned_to = inputValues?.supplier_details?.map((supplier) => {
        const supplierKey: string = getDirectaryKeyById(
          // supplier?.type_name === "1"
          //   ? "2"
          //   : (supplier?.type_name ?? "").toString(),
          Number(supplier?.supplier_dir_type),
          gConfig
        );
        // const dirType = getDirectaryNameById(
        //   supplier?.type_name === "1"
        //     ? "Employee"
        //     : (supplier?.type_name ?? "").toString(),
        //   gConfig
        // );
        // getDirectaryKeyById(
        //   Number(supplier?.dir_type),
        //   gConfig
        // );
        return {
          ...supplier,
          display_name: HtmlDecode(supplier?.display_name ?? ""),
          // display_name:
          //   HTMLEntities.decode(
          //     sanitizeString(supplier?.company_name?.trim())
          //   ) ?? "",
          type: supplier?.supplier_dir_type,
          type_key: supplierKey,
          user_id: Number(supplier.user_id),
        };
      });
      setSupplierTo(assigned_to as TselectedContactSendMail[]);
    }
  }, [inputValues?.supplier_details]);

  const handleSupplierTo = (data: TselectedContactSendMail[]) => {
    if (data?.length !== 0) {
      const assignedToArray = data?.map((user) => {
        const supplierKey: string = getDirectaryKeyById(
          Number(user?.type),
          gConfig
        );
        return {
          supplier_dir_type: supplierKey,
          type_name: user?.type_name,
          // directory_id: user.directory_id?.toString(),
          directory_id:
            user?.contact_id?.toString() == "0"
              ? user?.user_id?.toString()
              : user?.contact_id?.toString(),
          company_name: user?.company_name?.trim(),
          display_name: user?.display_name?.trim(),
          user_id: user?.user_id?.toString(),
        };
      });
      const suppliers = data
        ?.map((user) => {
          if (user?.contact_id == 0) {
            return user?.user_id?.toString();
          } else {
            return `${user?.user_id}|${user?.contact_id}`;
          }
        })
        ?.join(",");
      setInputValues({
        ...inputValues,
        supplier_details: assignedToArray,
        po_suppliers: data?.map((user) => user?.user_id)?.join(","),
      });
      handleUpdateField({
        po_suppliers: suppliers,
        supplier_id: Number(data?.[0]?.user_id || 0),
        // supplier_id: data?.map((user) => user?.user_id)?.join(","),
        // is_multiple_suppliers: data?.length > 1 ? 1 : 0,
        is_multiple_suppliers:
          isMultipleSupplier == -1
            ? purchaseOrderDetail?.is_multiple_suppliers
            : isMultipleSupplier,
        supplier_contact_id: Number(
          data?.filter((user) => user?.contact_id != 0)?.[0]?.contact_id || 0
        ),
        // supplier_contact_id: data
        // ?.filter((user) => user?.contact_id != 0)
        // ?.map((user) => user?.contact_id)
        // ?.join(","),
        purchase_order_id: Number(purchase_order_id) || 0,
      });
    } else {
      handleUpdateField({
        po_suppliers: "0",
        supplier_id: 0,
        purchase_order_id: Number(purchase_order_id) || 0,
      });
      handleChangeFieldStatus({
        field: "po_suppliers",
        status: "button",
        action: "BLUR",
      });
      setInputValues({
        ...inputValues,
        po_suppliers: "0",
        supplier_id: 0,
        supplier_details: [],
      });
    }
  };
  // const handleShiptoContact = (data: Partial<TselectedContactSendMail>) => {
  //   if (data?.user_id !== undefined) {
  //     if (data?.user_id != inputValues?.ship_to_contact) {
  //       setInputValues({
  //         ...inputValues,
  //         ship_to_contact: data?.user_id,
  //         ship_to_contact_name: data?.display_name,
  //         // issued_by_type:
  //         //   data?.orig_type?.toString() ||
  //         //   getDirectaryIdByKey(data.type_key as CustomerTabs, gConfig),
  //       });
  //       handleUpdateField({
  //         ship_to_contact: data?.user_id,
  //         // issued_by_contact: data?.contact_id?.toString(),
  //       });
  //     } else {
  //       handleChangeFieldStatus({
  //         field: "ship_to_contact",
  //         status: "button",
  //         action: "BLUR",
  //       });
  //       setInputValues({
  //         ...inputValues,
  //         ship_to_contact: purchaseOrderDetail?.ship_to_contact,
  //         ship_to_contact_name: purchaseOrderDetail?.ship_to_contact_name,
  //       });
  //     }
  //   } else {
  //     handleUpdateField({ ship_to_contact: "" });
  //     setInputValues({
  //       ...inputValues,
  //       ship_to_contact: "",
  //       ship_to_contact_name: "",
  //     });
  //   }
  // };

  const handleOrderFromBy = (data: Partial<TselectedContactSendMail>) => {
    if (data?.user_id !== undefined) {
      if (data?.user_id != inputValues.order_from) {
        setInputValues({
          ...inputValues,
          order_from: data?.user_id,
          order_from_username: data?.display_name,
          // issued_by_type:
          //   data?.orig_type?.toString() ||
          //   getDirectaryIdByKey(data.type_key as CustomerTabs, gConfig),
        });
        handleUpdateField({
          order_from: data?.user_id,
          // issued_by_contact: data?.contact_id?.toString(),
        });
      } else {
        handleChangeFieldStatus({
          field: "order_from",
          status: "button",
          action: "BLUR",
        });
        setInputValues({
          ...inputValues,
          order_from: purchaseOrderDetail?.order_from,
          order_from_username: purchaseOrderDetail?.order_from_username,
        });
      }
    } else {
      handleUpdateField({ order_from: "" });
      setInputValues({
        ...inputValues,
        order_from: "",
        order_from_username: "",
      });
    }
  };
  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Details")}
        headerProps={{
          containerClassName: "!flex-row !items-center",
        }}
        iconProps={{
          icon: "fa-solid fa-file-lines",
          containerClassName:
            "bg-[linear-gradient(180deg,#FF868A1a_0%,#FD33331a_100%)]",
          id: "details_icon",
          colors: ["#FF868A", "#FD3333"],
        }}
        headerRightButton={
          <div className="flex items-center gap-1.5 font-medium bg-blue-100 text-primary-900 py-[3px] px-[9px] rounded-sm dark:bg-dark-800 dark:text-white/90 whitespace-nowrap">
            {_t(isTaxEnabled ? "Total w/Tax: " : "Total: ")}
            {
              formatter(formatAmount(POMatrix?.allGrandTotal || 0))
                .value_with_symbol
            }
            {/* {totalPOAmount} */}
          </div>
        }
        children={
          <div className="pt-2">
            <ul className="w-full flex flex-col gap-1 mt-[3px]">
              <li>
                {/* <InlineField
                    label={_t("Supplier Option")}
                    labelPlacement="left"
                    field={
                      <RadioGroupList
                        view="row"
                        onChange={(e) => {
                          setIsMultipleSupplier(e?.target?.value);
                          setIsOpenSelectSupplierTo(true);
                          // handleUpdateField({
                          //   is_multiple_suppliers: Number(e?.target?.value),
                          //   po_suppliers: "0",
                          //   supplier_id: 0,
                          //   purchase_order_id: Number(purchase_order_id) || 0,
                          // });
                        }}
                        disabled={isReadOnly}
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "is_multiple_suppliers"
                        )}
                        value={purchaseOrderDetail?.is_multiple_suppliers}
                        options={[
                          {
                            label: _t("Individual Supplier"),
                            value: 0,
                          },
                          {
                            label: _t("Pricing Request (Multiple Suppliers)"),
                            value: 1,
                          },
                        ]}
                      />
                    }
                  /> */}
                <SelectField
                  label={_t("Type")}
                  labelPlacement="left"
                  placeholder={_t("Select Type")}
                  editInline={true}
                  value={purchaseOrderDetail?.is_multiple_suppliers?.toString()}
                  disabled={
                    isReadOnly ||
                    !["po_pricing_requested", "po_on_hold"]?.includes(
                      purchaseOrderDetail?.billing_status_key || ""
                    )
                  }
                  readOnly={
                    isReadOnly ||
                    !["po_pricing_requested", "po_on_hold"]?.includes(
                      purchaseOrderDetail?.billing_status_key || ""
                    )
                  }
                  iconView={true}
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "is_multiple_suppliers"
                  )}
                  options={OrderTypeList}
                  onChange={(val) => {
                    setIsMultipleSupplier(Number(val));
                    setIsOpenSelectSupplierTo(true);
                    // if (val) {
                    //   setFieldValue("type", val);
                    // } else {
                    //   setFieldValue("type", "");
                    // }
                  }}
                />
              </li>
              <li className="overflow-hidden">
                <ButtonField
                  label={_t("Supplier")}
                  placeholder={_t("Select/View Supplier")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  value={
                    purchaseOrderDetail?.supplier_details?.length &&
                    purchaseOrderDetail?.supplier_details?.length !== 0 &&
                    purchaseOrderDetail?.supplier_details.length > 2
                      ? `${purchaseOrderDetail.supplier_details?.length} Selected`
                      : HtmlDecode(
                          purchaseOrderDetail?.supplier_details
                            ?.map((supplier: IPoMultipleSupplier) => {
                              // const supllierType =
                              //   supplier?.supplier_dir_type;
                              // const supplierName =
                              //   supllierType == "22" || supllierType == "4" // Vendor || Contractor
                              // ?
                              //  supplier?.company_name
                              // : `${supplier?.first_name} ${supplier?.last_name}`;
                              // return supplier?.company_name;
                              return supplier?.display_name;
                            })
                            .join(", ")
                        )
                  }
                  avatarProps={
                    purchaseOrderDetail?.supplier_details?.length == 1
                      ? {
                          user: {
                            name: HtmlDecode(
                              purchaseOrderDetail?.supplier_details?.[0]
                                ?.display_name || ""
                            ),
                            image:
                              purchaseOrderDetail?.supplier_details?.[0]?.image,
                          },
                        }
                      : undefined
                  }
                  onClick={() => {
                    setIsOpenSelectSupplierTo(true);
                  }}
                  statusProps={{
                    status: getStatusForField(loadingStatus, "po_suppliers"),
                  }}
                  disabled={
                    getStatusForField(loadingStatus, "po_suppliers") ===
                      "loading" || isReadOnly
                  }
                  rightIcon={
                    <>
                      {inputValues?.supplier_details &&
                        inputValues?.supplier_details?.length == 1 && (
                          <div className="flex items-center gap-1">
                            <ContactDetailsButton
                              onClick={async () => {
                                await setAdditionContact(
                                  inputValues?.supplier_contact_id || 0
                                );
                                setIsContactDetails(true);
                                setcontactId(
                                  inputValues?.supplier_details?.[0]
                                    ?.user_id as number
                                );
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              directoryId={
                                inputValues?.supplier_details?.[0]?.user_id?.toString() as string
                              }
                              directoryTypeKey={
                                inputValues?.supplier_details?.[0]
                                  ?.supplier_dir_type !== "" &&
                                inputValues?.supplier_details?.[0]
                                  ?.supplier_dir_type
                                  ? getDirectaryKeyById(
                                      Number(
                                        inputValues?.supplier_details?.[0]
                                          ?.supplier_dir_type
                                      ),
                                      gConfig
                                    )?.toString()
                                  : ""
                              }
                              // directoryTypeKey={
                              //   inputValues?.supplier_details?.[0]?.dir_type !==
                              //     "" &&
                              //   inputValues?.supplier_details?.[0]?.dir_type
                              //     ? getDirectaryNameById(
                              //         inputValues?.supplier_details?.[0]
                              //           ?.type_name == "1"
                              //           ? "Employee"
                              //           : inputValues?.supplier_details?.[0]
                              //               ?.type_name,
                              //         gConfig
                              //       )?.toString()
                              //     : ""
                              // }
                            />
                          </div>
                        )}
                      {inputValues?.supplier_details &&
                        inputValues?.supplier_details?.length > 1 && (
                          <AvatarIconPopover
                            assignedTo={
                              inputValues?.supplier_details as IAssignedToUsers[]
                            }
                            setSelectedUserId={(data) => {
                              setcontactId(data?.id);
                              setAdditionContact(data?.contactId || 0);
                            }}
                            setIsOpenContactDetails={setIsContactDetails}
                            placement="bottom"
                            redirectionIcon={true}
                          />
                        )}
                    </>
                  }
                />
              </li>
              {/* <li>
                <SelectField
                  label={_t("Ship To")}
                  placeholder={_t("Select Ship To")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "ship_to")}
                  options={shipToOption}
                  onChange={(value: string | string[]) => {
                    handleUpdateField({
                      ship_to: Array.isArray(value) ? value?.[0] : value,
                      purchase_order_id: Number(purchase_order_id) || 0,
                      // customer_id: inputValues?.customer_id || "",
                    });
                    // handleChange({ value, name: "ship_to" });
                  }}
                  value={
                    purchaseOrderDetail?.ship_to &&
                    purchaseOrderDetail?.ship_to != ""
                      ? purchaseOrderDetail?.ship_to
                      : undefined
                  }
                />
              </li> */}
              {/* {purchaseOrderDetail?.ship_to === "directory_contact" ? (
                <li className="overflow-hidden">
                  <ButtonField
                    label={_t("Ship to Contact")}
                    placeholder={_t("Select Contact")}
                    labelPlacement="left"
                    editInline={true}
                    iconView={true}
                    required={false}
                    readOnly={isReadOnly}
                    onClick={() => {
                      setIsOpenShipToContact(true);
                    }}
                    value={replaceDOMParams(
                      sanitizeString(inputValues?.ship_to_contact_name || "")
                    )}
                    statusProps={{
                      status: getStatusForField(
                        loadingStatus,
                        "ship_to_contact"
                      ),
                    }}
                    disabled={
                      getStatusForField(loadingStatus, "ship_to_contact") ===
                      "loading"
                    }
                    rightIcon={
                      <div className="flex gap-1 items-center">
                        {inputValues?.ship_to_contact &&
                        loadingStatus?.find(
                          (el) => el.field === "ship_to_contact"
                        )?.status != "loading" ? (
                          <>
                            {" "}
                            <ContactDetailsButton
                              onClick={() => {
                                setIsOpenProjectManagerDetails(true);
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              directoryId={
                                inputValues?.ship_to_contact?.toString() || ""
                              }
                              directoryTypeKey={getDirectaryKeyById(
                                inputValues.supplier_dir_type === 1
                                  ? 2
                                  : Number(inputValues.supplier_dir_type),
                                gConfig
                              )}
                            />
                          </>
                        ) : (
                          <></>
                        )}
                      </div>
                    }
                  />
                </li>
              ) : (
                ""
              )} */}
              <li className="overflow-hidden">
                <ButtonField
                  label={_t("From")}
                  placeholder={_t("Select Employee")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  onClick={() => {
                    setIsOpenSelectOrderFrom(true);
                  }}
                  value={HtmlDecode(purchaseOrderDetail?.order_from_username)}
                  statusProps={{
                    status: getStatusForField(loadingStatus, "order_from"),
                  }}
                  disabled={
                    getStatusForField(loadingStatus, "order_from") ===
                      "loading" || isReadOnly
                  }
                  avatarProps={{
                    user: {
                      name: HtmlDecode(
                        purchaseOrderDetail?.order_from_username || ""
                      ),
                      image: purchaseOrderDetail?.order_from_image,
                    },
                  }}
                  rightIcon={
                    inputValues.order_from && inputValues.order_from !== "" ? (
                      <div className="flex items-center gap-1">
                        <ContactDetailsButton
                          onClick={async () => {
                            await setAdditionContact(Number(0));
                            setIsContactDetails(true);
                            setcontactId(inputValues?.order_from as number);
                          }}
                        />
                        <DirectoryFieldRedirectionIcon
                          directoryId={inputValues?.order_from.toString()}
                          directoryTypeKey={
                            inputValues?.order_from_dir_type?.toString() !==
                              "" && inputValues?.order_from_dir_type
                              ? getDirectaryKeyById(
                                  inputValues?.order_from_dir_type == 1
                                    ? 2
                                    : Number(inputValues?.order_from_dir_type),
                                  gConfig
                                )
                              : ""
                          }
                        />
                      </div>
                    ) : (
                      <></>
                    )
                  }
                  // <div className="flex gap-2 items-center">
                  //   <ButtonWithTooltip
                  //     tooltipTitle={_t("Contact Details")}
                  //     tooltipPlacement="top"
                  //     icon={faAddressCard}
                  //     onClick={() => {}}
                  //   />
                  //   <Tooltip
                  //     title={_t("Open the project detail in a new tab")}
                  //     placement="top"
                  //   >
                  //     <div>
                  //       <Link
                  //         href={"#"}
                  //         target="_blank"
                  //         title={
                  //           <FontAwesomeIcon
                  //             className="w-3 h-3 text-primary-900 dark:text-[#dcdcdd]"
                  //             icon={faArrowUpRightFromSquare}
                  //           />
                  //         }
                  //       />
                  //     </div>
                  //   </Tooltip>
                  // </div>
                />
              </li>
              <li>
                <DatePickerField
                  label={_t("Date")}
                  labelPlacement="left"
                  placeholder={_t("Select Date")}
                  editInline={true}
                  value={
                    inputValues?.order_date
                      ? displayDateFormat(
                          inputValues?.order_date?.toString()?.trim(),
                          date_format
                        )
                      : null
                  }
                  readOnly={isReadOnly}
                  iconView={true}
                  inputReadOnly={true}
                  allowClear={false}
                  format={date_format}
                  fixStatus={getStatusForField(loadingStatus, "order_date")}
                  onChange={(_, dateString) => {
                    if (
                      purchaseOrderDetail?.order_date !== dateString &&
                      !!dateString
                    ) {
                      // const poOrderDate = dayjs(
                      //   dateString?.toString(),
                      //   date_format
                      // );
                      // Check if expiration date exists
                      // const expireDate = purchaseOrderDetail?.expire_date
                      //   ? dayjs(purchaseOrderDetail?.expire_date, date_format)
                      //   : null;

                      // if (expireDate && expireDate.isBefore(poOrderDate)) {
                      //   // Expiration date exists and is in the future
                      //   notification.error({
                      //     description: _t(
                      //       "Estimate date should not be later than the expiration date"
                      //     ),
                      //   });
                      //   return;
                      // }
                      handleChangeDate(dateString as string, "order_date");
                    } else if (!dateString) {
                      // notification.error({
                      //   description: _t("Estimate date is required"),
                      // });
                      // handleChangeDate(
                      //   purchaseOrderDetail?.expire_date ?? "",
                      //   "estimate_date"
                      // );
                    }
                  }}
                />
              </li>
              <li>
                <DatePickerField
                  label={_t("Order Date")}
                  labelPlacement="left"
                  placeholder={_t("Select Order Date")}
                  editInline={true}
                  value={
                    inputValues?.po_order_date
                      ? displayDateFormat(
                          inputValues?.po_order_date?.toString()?.trim(),
                          date_format
                        )
                      : null
                  }
                  readOnly={isReadOnly}
                  iconView={true}
                  inputReadOnly={true}
                  allowClear={true}
                  format={date_format}
                  fixStatus={getStatusForField(loadingStatus, "po_order_date")}
                  onChange={(_, dateString) => {
                    // if (purchaseOrderDetail?.po_order_date !== dateString) {
                    //   handleChangeDate(dateString as string, "po_order_date");
                    // }
                    if (!!dateString) {
                      const deliveryDate = inputValues?.delivery_date
                        ? dayjs(inputValues?.delivery_date, date_format)
                        : null;
                      const expireDate = dayjs(
                        dateString?.toString(),
                        date_format
                      );

                      if (deliveryDate && deliveryDate?.isBefore(expireDate)) {
                        notification.error({
                          description: _t(
                            "Order Date must be less than or equal to Delivery Date."
                          ),
                        });
                        handleChangeFieldStatus({
                          field: "po_order_date",
                          status: "error",
                        });
                        resetFieldStatus("po_order_date");
                        return;
                      }
                      handleChangeDate(dateString?.toString(), "po_order_date");
                    } else {
                      handleChangeDate("", "po_order_date");
                    }
                  }}
                />
              </li>
              {/* <li>
                <DatePickerField
                  label={_t("Delivery Date")}
                  labelPlacement="left"
                  placeholder={_t("Select Delivery Date")}
                  editInline={true}
                  value={displayDateFormat(
                    purchaseOrderDetail?.delivery_date?.toString()?.trim(),
                    date_format
                  )}
                  readOnly={isReadOnly}
                  iconView={true}
                  inputReadOnly={true}
                  allowClear={true}
                  format={date_format}
                  fixStatus={getStatusForField(loadingStatus, "delivery_date")}
                  onChange={(_, dateString) => {
                    // if (purchaseOrderDetail?.delivery_date !== dateString) {
                    //   handleChangeDate(dateString as string, "delivery_date");
                    // }
                    if (!!dateString) {
                      const deliveryDate = inputValues?.delivery_date
                        ? dayjs(inputValues?.delivery_date, date_format)
                        : null;
                      const expireDate = dayjs(
                        dateString?.toString(),
                        date_format
                      );

                      if (deliveryDate && deliveryDate?.isAfter(expireDate)) {
                        notification.error({
                          description: _t(
                            "Delivery date must be greater than or equal to Order date."
                          ),
                        });
                        return;
                      }
                      handleChangeDate(dateString?.toString(), "delivery_date");
                    } else {
                      handleChangeDate("", "delivery_date");
                    }
                  }}
                />
              </li>
              <li className="overflow-hidden">
                <InputField
                  label={_t("Shipped Via")}
                  placeholder={_t("Shipped Via")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  value={HTMLEntities.decode(
                    sanitizeString(inputValues?.ship_via)
                  )}
                  onChange={(e) => handleInpOnChange(e, "ship_via")}
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "ship_via")}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "ship_via",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "ship_via",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "ship_via",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== purchaseOrderDetail?.ship_via) {
                      handleUpdateField({ ship_via: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "ship_via",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        ship_via: purchaseOrderDetail.ship_via,
                      });
                    }
                  }}
                />
              </li>
              <li className="overflow-hidden">
                <InputField
                  label={_t("FOB Point")}
                  placeholder={_t("FOB Point")}
                  value={HTMLEntities.decode(
                    sanitizeString(inputValues?.fob_point)
                  )}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  onChange={(e) => handleInpOnChange(e, "fob_point")}
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "fob_point")}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "fob_point",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "fob_point",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "fob_point",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== purchaseOrderDetail?.fob_point) {
                      if (/^(?!-?\d+$)[a-zA-Z0-9-]+$/.test(value)) {
                        handleUpdateField({ fob_point: value });
                      } else {
                        notification.error({
                          description: _t(
                            "FOB point must be greater than zero"
                          ),
                        });
                        dispatch(
                          updatePODetail({
                            fob_point: purchaseOrderDetail?.fob_point,
                          })
                        );
                        return;
                      }
                    } else {
                      handleChangeFieldStatus({
                        field: "fob_point",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        fob_point: purchaseOrderDetail?.fob_point,
                      });
                    }
                  }}
                />
              </li> */}
              <li>
                <SelectField
                  label={_t("Payment Terms")}
                  placeholder={_t("Select Payment Terms")}
                  value={
                    inputValues?.term_key ? inputValues.term_key : undefined
                  }
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  options={termsList}
                  readOnly={isReadOnly}
                  showSearch
                  allowClear
                  fixStatus={getStatusForField(loadingStatus, "term_id")}
                  disabled={
                    getStatusForField(loadingStatus, "term_id") === "loading"
                  }
                  filterOption={(input, option) =>
                    filterOptionBySubstring(input, option?.label as string)
                  }
                  onChange={(value: string | string[]) => {
                    // term_key is numeric then pass term_id is 0 else pass numeraic id
                    const isKeyValue = numOrTextFind(
                      Array.isArray(value) ? value[0] : value
                    );
                    handleChange({
                      value,
                      name: "term_id",
                      extraValue: {
                        term_key: isKeyValue?.toString(),
                        term_id: isKeyValue == "number" ? String(value) : "0",
                      },
                    });
                  }}
                  addItem={addItemObject}
                  onInputKeyDown={(e) => handlekeyDown(e)}
                  onClear={() => {
                    handleChange({
                      value: "",
                      name: "term_id",
                      extraValue: {
                        term_key: "",
                        term_id: "0",
                      },
                    });
                  }}
                />
              </li>
              <li className="overflow-hidden">
                <InputField
                  label={_t("Reference") + " #"}
                  placeholder={_t("Reference") + " #"}
                  labelPlacement="left"
                  value={HtmlDecode(inputValues?.reference_id)}
                  maxLength={21}
                  editInline={true}
                  iconView={true}
                  onChange={(e) => handleInpOnChange(e, "reference_id")}
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "reference_id")}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "reference_id",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "reference_id",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "reference_id",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== purchaseOrderDetail?.reference_id) {
                      handleUpdateField({ reference_id: HtmlDecode(value) });
                    } else {
                      handleChangeFieldStatus({
                        field: "reference_id",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        reference_id: purchaseOrderDetail.reference_id,
                      });
                    }
                  }}
                />
              </li>
              <li>
                <TextAreaField
                  label={_t("Description")}
                  placeholder={_t("Description")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  value={HtmlDecode(inputValues?.notes)}
                  onChange={(e) => handleInpOnChange(e, "notes")}
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "notes")}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "notes",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "notes",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "notes",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== purchaseOrderDetail?.notes) {
                      handleUpdateField({ notes: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "notes",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        notes: purchaseOrderDetail.notes,
                      });
                    }
                  }}
                />
              </li>
            </ul>
          </div>
        }
      />

      {isOpenSelectSupplierTo && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectSupplierTo}
          closeDrawer={() => {
            setIsOpenSelectSupplierTo(false);
            setIsMultipleSupplier(-1);
          }}
          singleSelecte={
            !!(isMultipleSupplier == -1
              ? purchaseOrderDetail?.is_multiple_suppliers == 0
              : isMultipleSupplier == 0)
          }
          // singleSelecte={isMultipleSupplier !== -1 ? purchaseOrderDetail?.is_multiple_suppliers == 0 : isMultipleSupplier == 0}
          setCustomer={(data) => {
            if (
              ![
                "po_approved",
                "po_submitted",
                "po_received",
                "po_paid",
              ]?.includes(purchaseOrderDetail?.billing_status_key || "")
            ) {
              handleSupplierTo(data);
            } else {
              notification.error({
                description: "Supplier field is required.",
              });
            }
          }}
          activeTab={CFConfig.vendor_key}
          // options={[...supplierOptionKeys]}
          selectedCustomer={
            isMultipleSupplier == -1 &&
            inputValues?.supplier_details &&
            inputValues?.supplier_details?.length !== 0
              ? supplierTo
              : []
          }
          options={supplierOptionKey}
          groupCheckBox={true}
          projectId={purchaseOrderDetail?.pro_id as number}
          // additionalContactDetails={0}
        />
      )}
      {isOpenProjectManagerDetails && (
        <ContactDetailsModal
          isOpenContact={isOpenProjectManagerDetails}
          onCloseModal={() => setIsOpenProjectManagerDetails(false)}
          contactId={Number(inputValues?.ship_to_contact)}
          additional_contact_id={0}
        />
      )}
      {/* {isOpenShipToContact && (
        <SelectCustomerDrawer
          projectId={Number(inputValues?.ship_to_contact)}
          openSelectCustomerSidebar={isOpenShipToContact}
          closeDrawer={() => {
            // dispatch(setActiveField(CFConfig.customer_key));
            setIsOpenShipToContact(false);
          }}
          singleSelecte={true}
          options={[CFConfig.employee_key, CFConfig.contractor_key]}
          setCustomer={(data) => {
            handleShiptoContact(
              data?.length ? (data[0] as Partial<TselectedContactSendMail>) : {}
            );
          }}
          selectedCustomer={
            inputValues.ship_to_contact &&
            inputValues?.ship_to_contact !== null &&
            inputValues?.ship_to_contact_name &&
            inputValues?.ship_to_contact_name !== ""
              ? ([
                  {
                    display_name: inputValues?.ship_to_contact_name,
                    user_id: inputValues?.ship_to_contact,
                    // customer_id: inputValues?.customer_id,
                    contact_id: inputValues?.ship_to_contact,
                    type: inputValues?.supplier_dir_type,
                    type_key: getDirectaryKeyById(
                      inputValues?.supplier_dir_type === 1
                        ? 2
                        : Number(inputValues?.supplier_dir_type),
                      gConfig
                    ),
                  },
                ] as TselectedContactSendMail[])
              : []
          }
          // selectedCustomer={isCustomerVal as TselectedContactSendMail[]}
          groupCheckBox={true}
          additionalContactDetails={0}
          app_access={true}
          canWrite={false}
        />
      )} */}
      {isOpenSelectOrderFrom && (
        <SelectCustomerDrawer
          projectId={Number(purchaseOrderDetail?.pro_id)}
          openSelectCustomerSidebar={isOpenSelectOrderFrom}
          closeDrawer={() => {
            // dispatch(setActiveField(CFConfig.customer_key));
            setIsOpenSelectOrderFrom(false);
          }}
          singleSelecte={true}
          options={[
            CFConfig.employee_key,
            ...(purchaseOrderDetail?.pro_id != 0
              ? ["my_project" as CustomerEmailTab]
              : []),
          ]}
          // options={[CFConfig.employee_key, "my_project"]}
          groupCheckBox={false}
          setCustomer={(data) => {
            if (data?.length) {
              handleOrderFromBy(
                data?.length
                  ? (data[0] as Partial<TselectedContactSendMail>)
                  : {}
              );
            } else {
              notification.error({
                description: "From field is required.",
              });
            }
          }}
          additionalContactDetails={0}
          selectedCustomer={
            purchaseOrderDetail?.order_from &&
            purchaseOrderDetail?.order_from !== null
              ? ([
                  {
                    display_name: purchaseOrderDetail?.order_from_username,
                    user_id: purchaseOrderDetail?.order_from,
                    // contact_id: purchaseOrderDetail?.customer_contact_id,
                    type: purchaseOrderDetail.order_from_dir_type || "",
                    type_key: getDirectaryKeyById(
                      purchaseOrderDetail.order_from_dir_type === 1
                        ? 2
                        : Number(purchaseOrderDetail.order_from_dir_type),
                      gConfig
                    ),
                    image: purchaseOrderDetail?.order_from_image,
                  },
                ] as TselectedContactSendMail[])
              : []
          }
        />
      )}

      {isContactDetails && (
        <ContactDetailsModal
          isOpenContact={isContactDetails}
          onCloseModal={() => {
            setcontactId(0);
            setIsContactDetails(false);
          }}
          contactId={contactId}
          readOnly={isReadOnly}
          additional_contact_id={additionalContact}
        />
      )}

      {/* Payment term term_id add popup */}
      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${HtmlDecode(
              customDataAdd?.name || ""
            )}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAddCustomData();
          }}
          onDecline={closeConfirmationModal}
        />
      )}
    </>
  );
};

export default DetailsCard;
